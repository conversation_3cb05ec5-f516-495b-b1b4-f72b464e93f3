<?php

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\DB;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

try {
    // Update the initial_bonus to 75
    DB::table('settings')->where('id', 1)->update(['initial_bonus' => 75]);
    
    // Clear cache
    \Illuminate\Support\Facades\Cache::flush();
    
    // Verify the change
    $setting = DB::table('settings')->where('id', 1)->first();
    
    echo "✅ Bônus atualizado com sucesso!\n";
    echo "📊 Valor atual: {$setting->initial_bonus}%\n";
    echo "🧮 Teste: R$ 2000 × 75% = R$ " . number_format((2000 * 75 / 100), 2, ',', '.') . "\n";
    echo "🧮 Teste: R$ 5000 × 75% = R$ " . number_format((5000 * 75 / 100), 2, ',', '.') . "\n";
    
} catch (Exception $e) {
    echo "❌ Erro: " . $e->getMessage() . "\n";
}
